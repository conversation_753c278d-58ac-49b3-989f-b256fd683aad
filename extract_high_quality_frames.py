#!/usr/bin/env python3
"""
高清晰度帧提取脚本
专门提取最清晰的帧，适合对图像质量要求很高的场景
"""

import argparse
from datetime import datetime
from train_inspection_extractor import TrainInspectionFrameExtractor


def extract_ultra_high_quality():
    """超高质量模式 - 只保留最清晰的帧"""
    
    print("🔍 超高质量帧提取模式")
    print("=" * 50)
    print("只保留最清晰的帧，适合精确分析")
    print()
    
    video_path = "assets/video/inspection.mp4"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"assets/frames_ultra_quality_{timestamp}"
    
    extractor = TrainInspectionFrameExtractor(
        video_path=video_path,
        output_dir=output_dir
    )
    
    # 超严格参数
    clear_frames = extractor.extract_clear_frames_for_inspection(
        quality_threshold_percentile=35,  # 只保留前65%最清晰的帧
        max_frames_per_second=5,          # 每秒只要5帧
        min_frame_interval=1              # 至少间隔5帧
    )
    
    print(f"✅ 提取了 {len(clear_frames)} 帧超高质量帧")
    
    # 保存帧
    frames_dir = extractor.save_inspection_frames(clear_frames, prefix="ultra_quality")
    extractor.create_inspection_report(clear_frames)
    extractor.plot_extraction_analysis(clear_frames)
    
    return frames_dir, len(clear_frames)


def extract_premium_quality():
    """高级质量模式 - 平衡质量和数量"""
    
    print("💎 高级质量帧提取模式")
    print("=" * 50)
    print("高质量帧，保持适当数量")
    print()
    
    video_path = "assets/video/inspection.mp4"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"assets/frames_premium_{timestamp}"
    
    extractor = TrainInspectionFrameExtractor(
        video_path=video_path,
        output_dir=output_dir
    )
    
    # 高质量参数
    clear_frames = extractor.extract_clear_frames_for_inspection(
        quality_threshold_percentile=25,  # 保留前75%最清晰的帧
        max_frames_per_second=8,          # 每秒8帧
        min_frame_interval=1              # 至少间隔3帧
    )
    
    print(f"✅ 提取了 {len(clear_frames)} 帧高级质量帧")
    
    # 保存帧
    frames_dir = extractor.save_inspection_frames(clear_frames, prefix="premium_quality")
    extractor.create_inspection_report(clear_frames)
    extractor.plot_extraction_analysis(clear_frames)
    
    return frames_dir, len(clear_frames)


def extract_custom_quality(quality_percentile=30, fps=6, interval=4):
    """自定义质量模式"""
    
    print(f"⚙️ 自定义质量模式")
    print("=" * 50)
    print(f"质量阈值: 前{100-quality_percentile}%")
    print(f"每秒帧数: {fps}")
    print(f"帧间隔: {interval}")
    print()
    
    video_path = "assets/video/inspection.mp4"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"assets/frames_custom_q{100-quality_percentile}_{timestamp}"
    
    extractor = TrainInspectionFrameExtractor(
        video_path=video_path,
        output_dir=output_dir
    )
    
    clear_frames = extractor.extract_clear_frames_for_inspection(
        quality_threshold_percentile=quality_percentile,
        max_frames_per_second=fps,
        min_frame_interval=interval
    )
    
    print(f"✅ 提取了 {len(clear_frames)} 帧自定义质量帧")
    
    # 保存帧
    frames_dir = extractor.save_inspection_frames(clear_frames, prefix="custom_quality")
    extractor.create_inspection_report(clear_frames)
    extractor.plot_extraction_analysis(clear_frames)
    
    return frames_dir, len(clear_frames)


def compare_quality_modes():
    """比较不同质量模式的效果"""
    
    print("📊 质量模式对比")
    print("=" * 60)
    
    results = {}
    
    # 运行不同质量模式
    modes = [
        ("超高质量", extract_ultra_high_quality),
        ("高级质量", extract_premium_quality),
        ("自定义质量", lambda: extract_custom_quality(30, 6, 4))
    ]
    
    for mode_name, extract_func in modes:
        print(f"\n🔄 运行 {mode_name} 模式...")
        try:
            frames_dir, frame_count = extract_func()
            results[mode_name] = {
                'frames_dir': frames_dir,
                'frame_count': frame_count
            }
        except Exception as e:
            print(f"❌ {mode_name} 模式失败: {e}")
            results[mode_name] = {'error': str(e)}
    
    # 显示对比结果
    print("\n" + "=" * 60)
    print("📈 质量模式对比结果:")
    print("=" * 60)
    
    for mode_name, result in results.items():
        if 'error' in result:
            print(f"{mode_name}: ❌ 失败 - {result['error']}")
        else:
            print(f"{mode_name}:")
            print(f"  📁 目录: {result['frames_dir']}")
            print(f"  🎯 帧数: {result['frame_count']}")
            print()
    
    print("💡 建议:")
    print("- 超高质量: 用于最精确的分析，帧数最少但质量最高")
    print("- 高级质量: 平衡质量和数量，推荐用于一般高质量需求")
    print("- 自定义质量: 根据具体需求调整参数")


def main():
    parser = argparse.ArgumentParser(description='高清晰度帧提取工具')
    parser.add_argument('--mode', 
                       choices=['ultra', 'premium', 'custom', 'compare'],
                       default='premium',
                       help='提取模式 (默认: premium)')
    
    parser.add_argument('--quality-percentile', type=int, default=30,
                       help='质量阈值百分位数 (仅custom模式, 默认: 30)')
    
    parser.add_argument('--fps', type=int, default=6,
                       help='每秒帧数 (仅custom模式, 默认: 6)')
    
    parser.add_argument('--interval', type=int, default=4,
                       help='最小帧间隔 (仅custom模式, 默认: 4)')
    
    args = parser.parse_args()
    
    print("🚄 高铁巡检 - 高清晰度帧提取工具")
    print("=" * 60)
    
    try:
        if args.mode == 'ultra':
            extract_ultra_high_quality()
        elif args.mode == 'premium':
            extract_premium_quality()
        elif args.mode == 'custom':
            extract_custom_quality(args.quality_percentile, args.fps, args.interval)
        elif args.mode == 'compare':
            compare_quality_modes()
        
        print("\n✅ 高清晰度帧提取完成!")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
