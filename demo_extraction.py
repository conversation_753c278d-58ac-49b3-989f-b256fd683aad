#!/usr/bin/env python3
"""
高铁巡检帧提取演示脚本
展示不同质量模式的输出目录结构
"""

import os
from datetime import datetime
from train_inspection_extractor import TrainInspectionFrameExtractor


def demo_different_quality_modes():
    """演示不同质量模式，每个都会创建独立的时间戳目录"""
    
    video_path = "assets/video/inspection.mp4"
    
    if not os.path.exists(video_path):
        print(f"❌ 演示需要视频文件: {video_path}")
        print("请确保视频文件存在")
        return
    
    # 不同的质量模式配置
    quality_modes = {
        'high_quality': {
            'quality_threshold_percentile': 25,
            'max_frames_per_second': 8,
            'min_frame_interval': 3,
            'description': '高质量模式 - 严格筛选，帧数较少但质量很高'
        },
        'balanced': {
            'quality_threshold_percentile': 15,
            'max_frames_per_second': 10,
            'min_frame_interval': 2,
            'description': '平衡模式 - 质量和数量的平衡'
        },
        'high_density': {
            'quality_threshold_percentile': 10,
            'max_frames_per_second': 15,
            'min_frame_interval': 1,
            'description': '高密度模式 - 保留更多帧用于全面分析'
        }
    }
    
    print("🚄 高铁巡检帧提取演示")
    print("=" * 60)
    print("将为每种质量模式创建独立的输出目录")
    print()
    
    results = {}
    
    for mode_name, config in quality_modes.items():
        print(f"📊 处理模式: {mode_name.upper()}")
        print(f"   {config['description']}")
        
        # 生成带时间戳的目录名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = f"assets/frames_{mode_name}_{timestamp}"
        
        print(f"   输出目录: {output_dir}")
        
        try:
            # 创建提取器
            extractor = TrainInspectionFrameExtractor(
                video_path=video_path,
                output_dir=output_dir
            )
            
            # 提取帧 (为演示目的，只处理前1000帧)
            print(f"   开始提取...")
            
            # 这里可以添加实际的提取逻辑
            # 为了演示，我们只创建目录结构
            os.makedirs(output_dir, exist_ok=True)
            os.makedirs(f"{output_dir}/frames", exist_ok=True)
            
            # 创建示例报告文件
            report_content = {
                'mode': mode_name,
                'timestamp': timestamp,
                'config': config,
                'output_directory': output_dir
            }
            
            import json
            with open(f"{output_dir}/demo_info.json", 'w', encoding='utf-8') as f:
                json.dump(report_content, f, indent=2, ensure_ascii=False)
            
            results[mode_name] = {
                'output_dir': output_dir,
                'timestamp': timestamp
            }
            
            print(f"   ✅ 目录创建完成")
            
        except Exception as e:
            print(f"   ❌ 错误: {e}")
        
        print()
    
    # 显示结果摘要
    print("=" * 60)
    print("📁 创建的目录结构:")
    print()
    
    for mode_name, result in results.items():
        print(f"{mode_name.upper()}:")
        print(f"  📂 {result['output_dir']}")
        print(f"     ├── frames/          # 提取的清晰帧")
        print(f"     ├── demo_info.json   # 演示信息")
        print(f"     ├── inspection_report.json  # (实际运行时生成)")
        print(f"     └── extraction_analysis.png # (实际运行时生成)")
        print()
    
    print("💡 说明:")
    print("- 每次运行都会创建新的时间戳目录")
    print("- 不同质量模式的结果互不干扰")
    print("- 所有输出都在 assets 目录下")
    print("- 可以方便地比较不同模式的效果")


def show_directory_structure():
    """显示预期的目录结构"""
    
    print("📁 高铁巡检项目目录结构")
    print("=" * 50)
    print("""
assets/
├── video/
│   └── inspection.mp4                    # 原始巡检视频
├── frames_balanced_20241231_143022/      # 平衡模式输出
│   ├── frames/
│   │   ├── train_inspection_000001_f00000123_t004.12s_q045.67.jpg
│   │   ├── train_inspection_000002_f00000156_t005.20s_q048.23.jpg
│   │   └── ...
│   ├── inspection_report.json
│   └── extraction_analysis.png
├── frames_high_quality_20241231_143055/  # 高质量模式输出
│   ├── frames/
│   │   ├── train_inspection_000001_f00000089_t002.97s_q052.34.jpg
│   │   └── ...
│   ├── inspection_report.json
│   └── extraction_analysis.png
└── frames_high_density_20241231_143128/  # 高密度模式输出
    ├── frames/
    │   ├── train_inspection_000001_f00000045_t001.50s_q041.23.jpg
    │   └── ...
    ├── inspection_report.json
    └── extraction_analysis.png
    """)
    
    print("🎯 目录命名规则:")
    print("- frames_{质量模式}_{YYYYMMDD_HHMMSS}")
    print("- 质量模式: balanced, high_quality, high_density, quick_review")
    print("- 时间戳: 年月日_时分秒")
    print()
    print("📊 文件命名规则:")
    print("- train_inspection_{序号}_f{原始帧号}_t{时间戳}s_q{清晰度分数}.jpg")
    print("- 序号: 6位数字 (000001, 000002, ...)")
    print("- 原始帧号: 8位数字")
    print("- 时间戳: 精确到小数点后2位")
    print("- 清晰度分数: 精确到小数点后2位")


if __name__ == "__main__":
    print("高铁巡检帧提取演示")
    print("请选择:")
    print("1. 显示目录结构说明")
    print("2. 演示创建不同质量模式的目录")
    print()
    
    choice = input("请输入选择 (1-2): ").strip()
    
    if choice == "1":
        show_directory_structure()
    elif choice == "2":
        demo_different_quality_modes()
    else:
        print("显示目录结构说明...")
        show_directory_structure()
