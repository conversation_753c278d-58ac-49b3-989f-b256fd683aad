# 高铁巡检视频清晰帧提取工具

专门为您的高铁巡检项目设计，从晃动的30fps视频中提取清晰帧用于目标识别。

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install opencv-python numpy matplotlib
```

### 2. 基本使用
```bash
# 使用默认平衡模式 (推荐)
python extract_train_frames.py

# 查看视频质量
python extract_train_frames.py --quick-check
```

## 📁 文件说明

- `extract_train_frames.py` - 主程序 (推荐使用)
- `train_inspection_extractor.py` - 核心提取器
- `inspection_config.py` - 配置文件
- `run_inspection_extraction.py` - 简化运行脚本

## ⚙️ 预设模式

### balanced (默认推荐)
- 保留质量前85%的帧
- 每秒10帧 (30fps → 10fps)
- 适合一般巡检目标识别

### high_quality (高质量)
- 保留质量前75%的帧
- 每秒8帧 (30fps → 8fps)
- 适合精确目标识别

### high_density (高密度)
- 保留质量前90%的帧
- 每秒15帧 (30fps → 15fps)
- 适合全面分析，保留更多帧

### quick_review (快速浏览)
- 保留质量前70%的帧
- 每秒5帧 (30fps → 5fps)
- 适合快速检查

## 🎯 使用示例

```bash
# 1. 检查视频质量
python extract_train_frames.py --quick-check

# 2. 标准提取 (推荐)
python extract_train_frames.py

# 3. 高质量模式 (更严格)
python extract_train_frames.py --preset high_quality

# 4. 高密度模式 (更多帧)
python extract_train_frames.py --preset high_density

# 5. 自定义输出目录
python extract_train_frames.py --output-dir my_frames

# 6. 查看所有预设
python extract_train_frames.py --list-presets
```

## 📊 输出结果

### 文件结构
```
inspection_clear_frames/
├── frames/                          # 提取的清晰帧
│   ├── train_inspection_000001_f00000123_t004.12s_q045.67.jpg
│   ├── train_inspection_000002_f00000156_t005.20s_q048.23.jpg
│   └── ...
├── inspection_report.json           # 详细分析报告
└── extraction_analysis.png          # 质量分析图表
```

### 文件名格式
`train_inspection_序号_f原始帧号_t时间戳_q清晰度分数.jpg`

例如: `train_inspection_000001_f00000123_t004.12s_q045.67.jpg`
- 序号: 000001
- 原始帧号: 00000123
- 时间戳: 4.12秒
- 清晰度分数: 45.67

## 🔧 参数调整建议

### 如果提取的帧太少
```bash
# 方法1: 使用高密度模式
python extract_train_frames.py --preset high_density

# 方法2: 修改 inspection_config.py 中的参数
quality_threshold_percentile = 10  # 降低到10 (保留前90%)
max_frames_per_second = 15         # 增加到15
```

### 如果提取的帧太多
```bash
# 方法1: 使用高质量模式
python extract_train_frames.py --preset high_quality

# 方法2: 修改配置参数
quality_threshold_percentile = 25  # 提高到25 (保留前75%)
max_frames_per_second = 8          # 减少到8
```

### 如果帧质量不够好
```bash
# 使用更严格的模糊检测
motion_blur_threshold = 30  # 降低阈值 (在config文件中)
```

## 📈 质量评估方法

工具使用组合算法评估帧清晰度:
- **拉普拉斯方差** (70%权重): 检测边缘清晰度
- **梯度幅值** (30%权重): 评估整体锐度

这种组合特别适合检测高铁设备的清晰轮廓。

## 💡 使用建议

### 针对目标识别优化
1. **首次使用**: 先运行 `--quick-check` 了解视频质量
2. **标准流程**: 使用默认 `balanced` 模式
3. **精确识别**: 如需要高精度，使用 `high_quality` 模式
4. **全面分析**: 如需要覆盖更多场景，使用 `high_density` 模式

### 处理大视频文件
- 工具会显示处理进度
- 建议在处理前检查磁盘空间
- 可以分段处理长视频

### 后续处理
提取的清晰帧可以直接用于:
- 目标检测模型训练
- 图像分类
- 特征提取
- 人工标注

## 🔍 故障排除

### 常见问题

**Q: 提示找不到视频文件**
A: 确保 `assets/video/inspection.mp4` 存在，或使用 `--video-path` 指定路径

**Q: 提取的帧数为0**
A: 视频可能质量很差，尝试使用 `--preset high_density` 或降低质量要求

**Q: 程序运行很慢**
A: 这是正常的，处理30fps视频需要时间。可以先用 `--quick-check` 估算处理时间

**Q: 内存不足**
A: 处理大视频时可能需要较多内存，建议关闭其他程序

### 性能优化
- 对于长视频，工具会自动显示进度
- 采用了优化的采样策略，避免处理所有帧
- 使用高效的OpenCV算法

## 📞 技术支持

如果遇到问题，请检查:
1. 视频文件格式 (推荐MP4)
2. Python环境和依赖包
3. 磁盘空间是否充足
4. 视频文件是否损坏

---

**专为高铁巡检项目优化 🚄**
