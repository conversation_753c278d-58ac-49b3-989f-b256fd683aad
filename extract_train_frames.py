#!/usr/bin/env python3
"""
高铁巡检视频清晰帧提取 - 主程序
使用配置文件，支持多种预设模式
"""

import argparse
import os
import sys
from datetime import datetime
from train_inspection_extractor import TrainInspectionFrameExtractor
from inspection_config import get_config, PRESET_CONFIGS, print_config_info


def main():
    parser = argparse.ArgumentParser(
        description='高铁巡检视频清晰帧提取工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python extract_train_frames.py                    # 使用默认平衡模式
  python extract_train_frames.py --preset high_quality  # 使用高质量模式
  python extract_train_frames.py --preset high_density  # 使用高密度模式
  python extract_train_frames.py --list-presets     # 查看所有预设
  python extract_train_frames.py --quick-check      # 快速质量检查

预设模式说明:
  balanced     - 平衡模式 (推荐): 质量和数量的平衡
  high_quality - 高质量模式: 更严格的质量要求，帧数较少
  high_density - 高密度模式: 保留更多帧，用于全面分析
  quick_review - 快速浏览模式: 少量高质量帧
        """
    )
    
    parser.add_argument('--preset', 
                       choices=list(PRESET_CONFIGS.keys()),
                       default='balanced',
                       help='选择预设配置 (默认: balanced)')
    
    parser.add_argument('--output-dir',
                       default=None,
                       help='输出目录 (默认: 自动生成带时间戳的目录)')
    
    parser.add_argument('--video-path',
                       default='assets/video/inspection.mp4',
                       help='视频文件路径 (默认: assets/video/inspection.mp4)')
    
    parser.add_argument('--list-presets',
                       action='store_true',
                       help='列出所有可用的预设配置')
    
    parser.add_argument('--quick-check',
                       action='store_true',
                       help='快速质量检查，不提取帧')
    
    parser.add_argument('--no-plot',
                       action='store_true',
                       help='不生成分析图表')
    
    parser.add_argument('--no-report',
                       action='store_true',
                       help='不生成分析报告')
    
    args = parser.parse_args()
    
    # 列出预设配置
    if args.list_presets:
        print_config_info()
        return 0
    
    # 检查视频文件
    if not os.path.exists(args.video_path):
        print(f"❌ 错误: 找不到视频文件 {args.video_path}")
        print("请确保视频文件存在或使用 --video-path 指定正确路径")
        return 1
    
    # 生成输出目录名 (带时间戳和质量模式)
    if args.output_dir is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = f"assets/frames_{args.preset}_{timestamp}"
    else:
        output_dir = args.output_dir

    # 获取配置
    config = get_config(args.preset)
    config['video']['input_path'] = args.video_path
    config['video']['output_dir'] = output_dir
    config['output']['generate_plots'] = not args.no_plot
    config['output']['generate_report'] = not args.no_report
    
    print("🚄 高铁巡检视频清晰帧提取工具")
    print("=" * 60)
    print(f"预设模式: {args.preset.upper()}")
    print(f"视频文件: {args.video_path}")
    print(f"输出目录: {output_dir}")
    print("=" * 60)
    
    try:
        # 创建提取器
        extractor = TrainInspectionFrameExtractor(
            video_path=config['video']['input_path'],
            output_dir=config['video']['output_dir']
        )
        
        # 快速质量检查模式
        if args.quick_check:
            print("\n🔍 执行快速质量检查...")
            analysis = extractor.analyze_video_quality(sample_interval=60)
            
            print(f"\n📊 视频质量报告:")
            print(f"  模糊帧比例: {analysis['blur_percentage']:.1f}%")
            print(f"  清晰度均值: {analysis['quality_stats']['mean']:.2f}")
            print(f"  清晰度标准差: {analysis['quality_stats']['std']:.2f}")
            print(f"  清晰度范围: {analysis['quality_stats']['min']:.2f} - {analysis['quality_stats']['max']:.2f}")
            
            # 给出建议
            if analysis['blur_percentage'] > 50:
                print("\n⚠️  建议: 视频模糊帧较多，推荐使用 'high_quality' 预设")
            elif analysis['blur_percentage'] > 30:
                print("\n💡 建议: 视频质量中等，'balanced' 预设应该适合")
            else:
                print("\n✅ 视频质量良好，可以使用 'high_density' 预设获得更多帧")
            
            return 0
        
        # 显示当前配置
        print(f"\n⚙️  当前配置:")
        extraction_config = config['extraction']
        print(f"  质量阈值百分位: {extraction_config['quality_threshold_percentile']}% "
              f"(保留前{100-extraction_config['quality_threshold_percentile']}%)")
        print(f"  每秒最大帧数: {extraction_config['max_frames_per_second']}")
        print(f"  最小帧间隔: {extraction_config['min_frame_interval']}")
        print(f"  模糊检测阈值: {extraction_config['motion_blur_threshold']}")
        
        # 开始提取
        print(f"\n📊 开始分析和提取帧...")
        clear_frames = extractor.extract_clear_frames_for_inspection(
            quality_threshold_percentile=extraction_config['quality_threshold_percentile'],
            max_frames_per_second=extraction_config['max_frames_per_second'],
            min_frame_interval=extraction_config['min_frame_interval']
        )
        
        if not clear_frames:
            print("❌ 没有提取到清晰帧")
            print("💡 建议:")
            print("  1. 降低质量要求: 使用 --preset high_density")
            print("  2. 检查视频质量: 使用 --quick-check")
            return 1
        
        # 保存帧
        print(f"\n💾 保存 {len(clear_frames)} 帧...")
        frames_dir = extractor.save_inspection_frames(
            clear_frames, 
            prefix=config['output']['filename_prefix']
        )
        
        # 生成报告
        if config['output']['generate_report']:
            print("📄 生成分析报告...")
            report_path = extractor.create_inspection_report(clear_frames)
        
        # 生成图表
        if config['output']['generate_plots']:
            print("📈 生成分析图表...")
            extractor.plot_extraction_analysis(clear_frames)
        
        # 输出结果摘要
        print("\n" + "=" * 60)
        print("✅ 提取完成!")
        print(f"📁 清晰帧目录: {frames_dir}")
        if config['output']['generate_report']:
            print(f"📄 分析报告: {report_path}")
        print(f"🎯 提取帧数: {len(clear_frames):,}")
        print(f"⏱️  平均帧率: {len(clear_frames)/extractor.duration:.1f} fps")
        print(f"📊 提取比例: {(len(clear_frames)/extractor.total_frames)*100:.2f}%")
        
        # 根据结果给出建议
        extraction_rate = len(clear_frames) / extractor.duration
        if extraction_rate < 3:
            print(f"\n💡 提示: 提取帧率较低 ({extraction_rate:.1f} fps)")
            print("   可以尝试使用 --preset high_density 获得更多帧")
        elif extraction_rate > 12:
            print(f"\n💡 提示: 提取帧率较高 ({extraction_rate:.1f} fps)")
            print("   如果帧数过多，可以尝试使用 --preset high_quality")
        
        print("=" * 60)
        
        return 0
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        print("\n💡 故障排除:")
        print("  1. 检查视频文件是否存在且可读")
        print("  2. 检查视频格式是否支持 (推荐 MP4)")
        print("  3. 确保有足够的磁盘空间")
        print("  4. 尝试使用 --quick-check 检查视频质量")
        return 1


if __name__ == "__main__":
    exit(main())
