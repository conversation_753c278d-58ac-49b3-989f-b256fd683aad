#!/usr/bin/env python3
"""
智能帧提取器 - 自动分析视频特征并选择最佳参数
无需手动调参，程序会智能分析视频质量并自动优化提取策略
"""

import cv2
import numpy as np
import os
import json
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端，避免卡住
import matplotlib.pyplot as plt
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Tuple


class SmartFrameExtractor:
    """智能帧提取器 - 自动分析并优化参数"""
    
    def __init__(self, video_path: str = "assets/video/inspection.mp4"):
        self.video_path = video_path
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.output_dir = Path(f"assets/frames_smart_{self.timestamp}")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 检查视频
        if not os.path.exists(video_path):
            raise FileNotFoundError(f"视频文件不存在: {video_path}")
        
        self.cap = cv2.VideoCapture(video_path)
        if not self.cap.isOpened():
            raise ValueError(f"无法打开视频: {video_path}")
        
        # 获取视频基本信息
        self.fps = self.cap.get(cv2.CAP_PROP_FPS)
        self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
        self.width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        self.height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        self.duration = self.total_frames / self.fps
        
        print(f"🚄 智能帧提取器")
        print(f"视频: {video_path}")
        print(f"分辨率: {self.width}x{self.height}")
        print(f"帧率: {self.fps:.1f} FPS")
        print(f"时长: {self.duration:.1f}秒")
        print(f"总帧数: {self.total_frames:,}")
        print(f"输出: {self.output_dir}")
        print("=" * 50)
    
    def calculate_sharpness(self, frame: np.ndarray) -> float:
        """计算帧清晰度 - 使用优化的组合算法"""
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY) if len(frame.shape) == 3 else frame
        
        # 拉普拉斯方差 - 主要指标
        laplacian = cv2.Laplacian(gray, cv2.CV_64F)
        lap_var = laplacian.var()
        
        # 梯度幅值 - 辅助指标
        grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
        grad_mag = np.sqrt(grad_x**2 + grad_y**2).mean()
        
        # 组合分数 (拉普拉斯权重更高)
        return lap_var * 0.8 + grad_mag * 0.2
    
    def analyze_video_quality(self) -> Dict:
        """智能分析视频质量特征"""
        print("🔍 智能分析视频质量...")
        
        # 采样策略：根据视频长度动态调整
        if self.duration <= 60:
            sample_interval = max(1, int(self.fps // 6))  # 短视频密集采样
        elif self.duration <= 300:
            sample_interval = max(1, int(self.fps // 4))  # 中等视频
        else:
            sample_interval = max(1, int(self.fps // 2))  # 长视频稀疏采样
        
        print(f"采样间隔: 每{sample_interval}帧")
        
        frame_count = 0
        quality_scores = []
        motion_scores = []
        brightness_scores = []
        
        self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
        
        while True:
            ret, frame = self.cap.read()
            if not ret:
                break
            
            if frame_count % sample_interval == 0:
                # 清晰度分析
                sharpness = self.calculate_sharpness(frame)
                quality_scores.append(sharpness)
                
                # 运动分析 (检测模糊程度)
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                motion_blur = cv2.Laplacian(gray, cv2.CV_64F).var()
                motion_scores.append(motion_blur)
                
                # 亮度分析
                brightness = gray.mean()
                brightness_scores.append(brightness)
            
            frame_count += 1
            
            if frame_count % (int(self.fps) * 30) == 0:  # 每30秒报告
                print(f"  已分析: {frame_count/self.fps:.0f}秒")
        
        # 统计分析
        quality_scores = np.array(quality_scores)
        motion_scores = np.array(motion_scores)
        brightness_scores = np.array(brightness_scores)
        
        analysis = {
            'quality_stats': {
                'mean': float(quality_scores.mean()),
                'std': float(quality_scores.std()),
                'min': float(quality_scores.min()),
                'max': float(quality_scores.max()),
                'q25': float(np.percentile(quality_scores, 25)),
                'q50': float(np.percentile(quality_scores, 50)),
                'q75': float(np.percentile(quality_scores, 75)),
                'q90': float(np.percentile(quality_scores, 90))
            },
            'motion_stats': {
                'mean': float(motion_scores.mean()),
                'std': float(motion_scores.std()),
                'blur_ratio': float(np.sum(motion_scores < motion_scores.mean() * 0.5) / len(motion_scores))
            },
            'brightness_stats': {
                'mean': float(brightness_scores.mean()),
                'std': float(brightness_scores.std())
            },
            'sample_count': len(quality_scores)
        }
        
        print(f"✅ 质量分析完成 (采样{len(quality_scores)}帧)")
        print(f"清晰度: {analysis['quality_stats']['mean']:.1f} ± {analysis['quality_stats']['std']:.1f}")
        print(f"模糊帧比例: {analysis['motion_stats']['blur_ratio']*100:.1f}%")
        
        return analysis
    
    def smart_parameter_selection(self, analysis: Dict) -> Dict:
        """基于分析结果智能选择参数"""
        print("🧠 智能参数优化...")
        
        quality_stats = analysis['quality_stats']
        motion_stats = analysis['motion_stats']
        
        # 评估视频质量等级
        quality_cv = quality_stats['std'] / quality_stats['mean']  # 变异系数
        blur_ratio = motion_stats['blur_ratio']
        
        # 智能参数选择逻辑
        if blur_ratio > 0.4:  # 高模糊比例
            quality_level = "poor"
            quality_threshold_percentile = 5   # 保留95%，因为整体质量差
            max_fps = 15                       # 多保留一些帧
            min_interval = 1                   # 允许密集提取
            motion_threshold = 20              # 宽松的模糊检测
        elif blur_ratio > 0.2:  # 中等模糊
            quality_level = "medium"
            quality_threshold_percentile = 15  # 保留85%
            max_fps = 10                       # 标准密度
            min_interval = 2                   # 适中间隔
            motion_threshold = 40              # 中等模糊检测
        else:  # 低模糊，高质量
            quality_level = "good"
            quality_threshold_percentile = 25  # 保留75%，可以更挑剔
            max_fps = 8                        # 较少帧数但高质量
            min_interval = 3                   # 较大间隔
            motion_threshold = 60              # 严格模糊检测
        
        # 根据质量分布调整
        if quality_cv > 0.5:  # 质量变化很大
            max_fps += 2  # 增加帧数以覆盖质量好的片段
        
        # 根据视频长度调整
        if self.duration > 300:  # 长视频
            max_fps = max(5, max_fps - 2)  # 减少帧数
        elif self.duration < 60:  # 短视频
            max_fps += 3  # 增加帧数
        
        params = {
            'quality_level': quality_level,
            'quality_threshold_percentile': quality_threshold_percentile,
            'max_frames_per_second': max_fps,
            'min_frame_interval': min_interval,
            'motion_blur_threshold': motion_threshold,
            'reasoning': {
                'blur_ratio': blur_ratio,
                'quality_cv': quality_cv,
                'duration': self.duration
            }
        }
        
        print(f"视频质量等级: {quality_level.upper()}")
        print(f"智能参数:")
        print(f"  质量阈值: 保留前{100-quality_threshold_percentile}%")
        print(f"  每秒帧数: {max_fps}")
        print(f"  帧间隔: {min_interval}")
        print(f"  模糊阈值: {motion_threshold}")
        
        return params
    
    def extract_frames_with_smart_params(self, params: Dict) -> List[Dict]:
        """使用智能参数提取帧"""
        print("🎯 开始智能提取帧...")
        
        # 重新分析所有帧以获得精确的质量分布
        self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
        
        all_frames = []
        frame_count = 0
        
        # 第一遍：收集所有帧的质量信息
        print("第一遍扫描: 评估所有帧质量...")
        while True:
            ret, frame = self.cap.read()
            if not ret:
                break
            
            sharpness = self.calculate_sharpness(frame)
            timestamp = frame_count / self.fps
            
            all_frames.append({
                'frame_number': frame_count,
                'timestamp': timestamp,
                'sharpness': sharpness,
                'frame': None  # 暂不保存帧数据
            })
            
            frame_count += 1
            
            if frame_count % (int(self.fps) * 30) == 0:
                print(f"  进度: {frame_count/self.fps:.0f}秒")
        
        # 计算质量阈值
        sharpness_values = [f['sharpness'] for f in all_frames]
        threshold_value = np.percentile(sharpness_values, params['quality_threshold_percentile'])
        
        print(f"质量阈值: {threshold_value:.2f}")
        
        # 第二遍：根据智能参数选择帧
        print("第二遍扫描: 智能选择帧...")
        self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
        
        selected_frames = []
        last_selected_frame = -params['min_frame_interval']
        frames_in_current_second = 0
        current_second = 0
        
        for frame_info in all_frames:
            frame_number = frame_info['frame_number']
            frame_second = int(frame_info['timestamp'])
            
            # 重置每秒计数
            if frame_second > current_second:
                current_second = frame_second
                frames_in_current_second = 0
            
            # 检查选择条件
            should_select = (
                frame_info['sharpness'] >= threshold_value and
                frame_number - last_selected_frame >= params['min_frame_interval'] and
                frames_in_current_second < params['max_frames_per_second']
            )
            
            if should_select:
                # 读取实际帧数据
                self.cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
                ret, frame = self.cap.read()
                
                if ret:
                    # 最后的模糊检测
                    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                    motion_blur_score = cv2.Laplacian(gray, cv2.CV_64F).var()
                    
                    if motion_blur_score >= params['motion_blur_threshold']:
                        frame_info['frame'] = frame.copy()
                        selected_frames.append(frame_info)
                        last_selected_frame = frame_number
                        frames_in_current_second += 1
        
        print(f"✅ 智能提取完成!")
        print(f"总帧数: {len(all_frames):,}")
        print(f"选中帧数: {len(selected_frames):,}")
        print(f"选择率: {len(selected_frames)/len(all_frames)*100:.2f}%")
        print(f"平均帧率: {len(selected_frames)/self.duration:.1f} fps")
        
        return selected_frames
    
    def save_frames(self, frames: List[Dict]) -> Path:
        """保存提取的帧"""
        frames_dir = self.output_dir / "frames"
        frames_dir.mkdir(exist_ok=True)
        
        print(f"💾 保存 {len(frames)} 帧...")
        
        for i, frame_data in enumerate(sorted(frames, key=lambda x: x['timestamp'])):
            filename = (f"smart_{i+1:06d}_"
                       f"f{frame_data['frame_number']:08d}_"
                       f"t{frame_data['timestamp']:07.2f}s_"
                       f"q{frame_data['sharpness']:06.2f}.jpg")
            
            filepath = frames_dir / filename
            cv2.imwrite(str(filepath), frame_data['frame'], 
                       [cv2.IMWRITE_JPEG_QUALITY, 95])
        
        print(f"✅ 帧已保存到: {frames_dir}")
        return frames_dir
    
    def create_analysis_report(self, analysis: Dict, params: Dict, frames: List[Dict]) -> str:
        """创建智能分析报告"""
        report = {
            'extraction_info': {
                'timestamp': self.timestamp,
                'video_path': self.video_path,
                'extraction_mode': 'smart_auto',
                'total_frames_extracted': len(frames),
                'extraction_rate_fps': len(frames) / self.duration,
                'extraction_percentage': (len(frames) / self.total_frames) * 100
            },
            'video_analysis': analysis,
            'smart_parameters': params,
            'quality_summary': {
                'video_quality_level': params['quality_level'],
                'recommended_use': self._get_usage_recommendation(params['quality_level']),
                'frame_quality_range': {
                    'min': float(min(f['sharpness'] for f in frames)),
                    'max': float(max(f['sharpness'] for f in frames)),
                    'mean': float(np.mean([f['sharpness'] for f in frames]))
                }
            }
        }
        
        report_path = self.output_dir / "smart_analysis_report.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"📄 智能分析报告: {report_path}")
        return str(report_path)
    
    def _get_usage_recommendation(self, quality_level: str) -> str:
        """根据质量等级给出使用建议"""
        recommendations = {
            'poor': '建议用于粗略检查，可能需要人工复核',
            'medium': '适合一般目标识别任务',
            'good': '适合精确分析和高质量目标识别'
        }
        return recommendations.get(quality_level, '未知质量等级')
    
    def create_visualization(self, frames: List[Dict]) -> str:
        """创建可视化分析图"""
        if not frames:
            return ""
        
        timestamps = [f['timestamp'] for f in frames]
        sharpness_scores = [f['sharpness'] for f in frames]
        
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))
        
        # 时间分布
        ax1.scatter(timestamps, sharpness_scores, alpha=0.6, s=30, c='blue')
        ax1.set_xlabel('时间 (秒)')
        ax1.set_ylabel('清晰度分数')
        ax1.set_title('智能提取帧 - 时间分布')
        ax1.grid(True, alpha=0.3)
        
        # 质量分布
        ax2.hist(sharpness_scores, bins=30, alpha=0.7, color='green', edgecolor='black')
        ax2.set_xlabel('清晰度分数')
        ax2.set_ylabel('帧数')
        ax2.set_title('清晰度分数分布')
        ax2.grid(True, alpha=0.3)
        
        mean_quality = np.mean(sharpness_scores)
        ax2.axvline(mean_quality, color='red', linestyle='--', 
                   label=f'平均值: {mean_quality:.2f}')
        ax2.legend()
        
        plt.tight_layout()
        
        plot_path = self.output_dir / "smart_analysis.png"
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"📈 可视化图表: {plot_path}")
        return str(plot_path)
    
    def run_smart_extraction(self):
        """运行完整的智能提取流程"""
        try:
            # 1. 智能分析视频
            analysis = self.analyze_video_quality()
            
            # 2. 智能选择参数
            params = self.smart_parameter_selection(analysis)
            
            # 3. 智能提取帧
            frames = self.extract_frames_with_smart_params(params)
            
            if not frames:
                print("❌ 未提取到任何帧，视频质量可能过低")
                return None
            
            # 4. 保存结果
            frames_dir = self.save_frames(frames)
            report_path = self.create_analysis_report(analysis, params, frames)
            plot_path = self.create_visualization(frames)
            
            # 5. 输出摘要
            print("\n" + "=" * 60)
            print("🎉 智能提取完成!")
            print(f"📁 帧目录: {frames_dir}")
            print(f"📄 分析报告: {report_path}")
            print(f"📈 可视化图表: {plot_path}")
            print(f"🎯 提取帧数: {len(frames):,}")
            print(f"⏱️  平均帧率: {len(frames)/self.duration:.1f} fps")
            print(f"🏆 视频质量: {params['quality_level'].upper()}")
            print("=" * 60)
            
            return {
                'frames_dir': frames_dir,
                'report_path': report_path,
                'plot_path': plot_path,
                'frame_count': len(frames),
                'quality_level': params['quality_level']
            }
            
        except Exception as e:
            print(f"❌ 智能提取失败: {e}")
            return None
        
        finally:
            if hasattr(self, 'cap'):
                self.cap.release()


def main():
    """主函数"""
    print("🤖 智能帧提取器 - 无需调参，自动优化")
    print("=" * 60)
    
    video_path = "assets/video/inspection.mp4"
    
    if not os.path.exists(video_path):
        print(f"❌ 找不到视频文件: {video_path}")
        return 1
    
    try:
        extractor = SmartFrameExtractor(video_path)
        result = extractor.run_smart_extraction()
        
        if result:
            print("\n💡 使用建议:")
            if result['quality_level'] == 'good':
                print("- 视频质量良好，提取的帧适合精确分析")
            elif result['quality_level'] == 'medium':
                print("- 视频质量中等，提取的帧适合一般目标识别")
            else:
                print("- 视频质量较差，建议检查拍摄条件或考虑重新录制")
            
            return 0
        else:
            return 1
            
    except Exception as e:
        print(f"❌ 程序错误: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
