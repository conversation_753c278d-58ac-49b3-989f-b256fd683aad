#!/usr/bin/env python3
"""
高铁巡检视频清晰帧提取 - 运行脚本
针对您的具体需求优化配置
"""

from train_inspection_extractor import TrainInspectionFrameExtractor
import os
from datetime import datetime


def extract_inspection_frames():
    """提取巡检清晰帧 - 为目标识别优化"""
    
    print("🚄 高铁巡检视频清晰帧提取")
    print("=" * 50)
    
    # 检查视频文件
    video_path = "assets/video/inspection.mp4"
    if not os.path.exists(video_path):
        print(f"❌ 错误: 找不到视频文件 {video_path}")
        print("请确保视频文件在正确位置")
        return
    
    try:
        # 生成带时间戳的输出目录
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = f"assets/frames_balanced_{timestamp}"

        # 创建提取器
        extractor = TrainInspectionFrameExtractor(
            video_path=video_path,
            output_dir=output_dir
        )
        
        print("\n📊 开始分析和提取...")
        
        # 为巡检目标识别优化的参数设置
        clear_frames = extractor.extract_clear_frames_for_inspection(
            quality_threshold_percentile=15,  # 保留质量前85%的帧 (保留更多)
            max_frames_per_second=10,         # 每秒最多10帧 (30fps -> 10fps)
            min_frame_interval=2              # 最小间隔2帧 (避免重复)
        )
        
        if not clear_frames:
            print("❌ 没有提取到清晰帧，请检查视频质量或调整参数")
            return
        
        print(f"\n💾 保存 {len(clear_frames)} 帧...")
        
        # 保存帧
        frames_dir = extractor.save_inspection_frames(clear_frames, prefix="train_inspection")
        
        # 创建详细报告
        report_path = extractor.create_inspection_report(clear_frames)
        
        # 生成分析图表
        print("\n📈 生成分析图表...")
        extractor.plot_extraction_analysis(clear_frames)
        
        # 输出结果摘要
        print("\n" + "=" * 50)
        print("✅ 提取完成!")
        print(f"📁 清晰帧目录: {frames_dir}")
        print(f"📄 分析报告: {report_path}")
        print(f"🎯 提取帧数: {len(clear_frames):,}")
        print(f"⏱️  平均帧率: {len(clear_frames)/extractor.duration:.1f} fps")
        print(f"📊 提取比例: {(len(clear_frames)/extractor.total_frames)*100:.2f}%")
        print("=" * 50)
        
        # 给出后续建议
        print("\n💡 后续建议:")
        print("1. 检查提取的帧质量是否满足目标识别需求")
        print("2. 如果帧数太少，可以降低 quality_threshold_percentile")
        print("3. 如果帧数太多，可以提高 quality_threshold_percentile 或减少 max_frames_per_second")
        print("4. 可以使用这些清晰帧训练或测试您的目标识别模型")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        print("请检查视频文件是否损坏或格式是否支持")


def extract_with_custom_settings():
    """使用自定义设置提取"""
    
    print("🔧 自定义设置提取")
    print("=" * 50)
    
    # 您可以根据需要调整这些参数
    settings = {
        'quality_threshold_percentile': 10,  # 保留质量前90%的帧
        'max_frames_per_second': 15,         # 每秒最多15帧
        'min_frame_interval': 1              # 最小间隔1帧
    }
    
    print(f"设置参数:")
    for key, value in settings.items():
        print(f"  {key}: {value}")
    
    video_path = "assets/video/inspection.mp4"
    
    try:
        # 生成带时间戳的输出目录
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = f"assets/frames_custom_{timestamp}"

        extractor = TrainInspectionFrameExtractor(
            video_path=video_path,
            output_dir=output_dir
        )
        
        clear_frames = extractor.extract_clear_frames_for_inspection(**settings)
        
        frames_dir = extractor.save_inspection_frames(clear_frames, prefix="custom_inspection")
        report_path = extractor.create_inspection_report(clear_frames)
        extractor.plot_extraction_analysis(clear_frames)
        
        print(f"\n✅ 自定义提取完成! 提取了 {len(clear_frames)} 帧")
        
    except Exception as e:
        print(f"❌ 错误: {e}")


def quick_quality_check():
    """快速质量检查"""
    
    print("🔍 快速质量检查")
    print("=" * 50)
    
    video_path = "assets/video/inspection.mp4"
    
    try:
        # 质量检查使用简单的目录名
        output_dir = "assets/quality_check"

        extractor = TrainInspectionFrameExtractor(
            video_path=video_path,
            output_dir=output_dir
        )
        
        # 只做质量分析，不提取帧
        analysis = extractor.analyze_video_quality(sample_interval=60)  # 每2秒采样
        
        print(f"\n📊 视频质量报告:")
        print(f"  模糊帧比例: {analysis['blur_percentage']:.1f}%")
        print(f"  清晰度均值: {analysis['quality_stats']['mean']:.2f}")
        print(f"  清晰度标准差: {analysis['quality_stats']['std']:.2f}")
        print(f"  清晰度范围: {analysis['quality_stats']['min']:.2f} - {analysis['quality_stats']['max']:.2f}")
        
        # 给出建议
        if analysis['blur_percentage'] > 50:
            print("\n⚠️  警告: 视频模糊帧较多，建议调整提取参数")
        elif analysis['blur_percentage'] > 30:
            print("\n💡 建议: 视频质量中等，可以适当提高质量阈值")
        else:
            print("\n✅ 视频质量良好，可以使用默认参数")
            
    except Exception as e:
        print(f"❌ 错误: {e}")


if __name__ == "__main__":
    print("高铁巡检视频处理工具")
    print("请选择操作:")
    print("1. 标准提取 (推荐)")
    print("2. 自定义设置提取")
    print("3. 快速质量检查")
    print()
    
    choice = input("请输入选择 (1-3): ").strip()
    
    if choice == "1":
        extract_inspection_frames()
    elif choice == "2":
        extract_with_custom_settings()
    elif choice == "3":
        quick_quality_check()
    else:
        print("直接运行标准提取...")
        extract_inspection_frames()
