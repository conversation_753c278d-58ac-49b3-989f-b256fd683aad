# 🤖 智能帧提取器

专为高铁巡检项目设计的智能帧提取工具，**无需手动调参**，程序会自动分析视频特征并选择最佳提取策略。

## ✨ 核心特性

- **🧠 智能分析**: 自动评估视频质量、模糊程度、亮度分布
- **⚙️ 自动调参**: 根据分析结果智能选择最佳参数
- **🎯 精准提取**: 针对不同质量视频采用不同策略
- **📊 详细报告**: 生成完整的分析报告和可视化图表
- **🚫 无需调参**: 一键运行，自动优化

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install opencv-python numpy matplotlib
```

### 2. 运行智能提取
```bash
python smart_frame_extractor.py
```

就这么简单！程序会自动：
- 分析您的视频质量
- 选择最佳提取参数
- 提取最清晰的帧
- 生成分析报告

## 🧠 智能算法

### 自动质量评估
程序会分析：
- **清晰度分布**: 使用拉普拉斯方差+梯度幅值组合算法
- **运动模糊**: 检测晃动造成的模糊帧
- **亮度分析**: 评估曝光情况
- **质量变异**: 分析质量稳定性

### 智能参数选择
根据分析结果自动调整：

| 视频质量 | 策略 | 参数调整 |
|---------|------|---------|
| **高质量** | 精选模式 | 严格筛选，保留75%，每秒8帧 |
| **中等质量** | 平衡模式 | 适中筛选，保留85%，每秒10帧 |
| **低质量** | 宽松模式 | 宽松筛选，保留95%，每秒15帧 |

### 动态优化
- **长视频**: 自动减少帧密度
- **短视频**: 自动增加帧密度  
- **质量波动大**: 增加帧数覆盖优质片段
- **质量稳定**: 减少冗余帧

## 📊 输出结果

每次运行会在 `assets` 下创建时间戳目录：

```
assets/
└── frames_smart_20241231_143022/
    ├── frames/                      # 提取的清晰帧
    │   ├── smart_000001_f00000123_t004.12s_q045.67.jpg
    │   ├── smart_000002_f00000156_t005.20s_q048.23.jpg
    │   └── ...
    ├── smart_analysis_report.json   # 智能分析报告
    └── smart_analysis.png          # 可视化图表
```

### 文件命名
- `smart_序号_f原始帧号_t时间戳_q清晰度分数.jpg`
- 例如: `smart_000001_f00000123_t004.12s_q045.67.jpg`

## 📈 分析报告

智能分析报告包含：

```json
{
  "extraction_info": {
    "extraction_mode": "smart_auto",
    "total_frames_extracted": 1250,
    "extraction_rate_fps": 8.5
  },
  "video_analysis": {
    "quality_stats": { "mean": 297.5, "std": 85.2 },
    "motion_stats": { "blur_ratio": 0.15 }
  },
  "smart_parameters": {
    "quality_level": "good",
    "quality_threshold_percentile": 25,
    "max_frames_per_second": 8
  },
  "quality_summary": {
    "recommended_use": "适合精确分析和高质量目标识别"
  }
}
```

## 🎯 适用场景

### 高铁巡检项目
- ✅ 自动识别设备清晰图像
- ✅ 过滤晃动模糊帧
- ✅ 保持时间覆盖完整性
- ✅ 适配不同拍摄条件

### 其他应用
- 监控视频关键帧提取
- 行车记录仪事件检测
- 工业检测视频分析
- 医疗影像质量筛选

## 🔍 与传统方法对比

| 特性 | 传统方法 | 智能提取器 |
|------|---------|-----------|
| **参数调整** | 需要手动试错 | 自动优化 |
| **适应性** | 固定参数 | 动态调整 |
| **质量评估** | 单一指标 | 多维分析 |
| **使用难度** | 需要专业知识 | 一键运行 |
| **结果稳定性** | 依赖经验 | 算法保证 |

## 💡 使用建议

1. **首次使用**: 直接运行，查看分析报告了解视频特征
2. **批量处理**: 程序会为每个视频自动选择最佳策略
3. **质量检查**: 查看生成的可视化图表验证提取效果
4. **后续处理**: 提取的帧可直接用于目标识别模型

## 🛠️ 故障排除

### 常见问题

**Q: 提取帧数太少**
A: 程序检测到视频质量较差，这是正常的保护机制

**Q: 提取帧数太多**  
A: 程序检测到视频质量很好，会保留更多高质量帧

**Q: 程序运行时间长**
A: 智能分析需要扫描整个视频，这是为了获得最佳效果

### 性能优化
- 程序会根据视频长度自动调整采样策略
- 使用非交互式图表后端，避免界面卡住
- 内存优化的两遍扫描算法

---

**🤖 智能化，零调参，专业效果！**
