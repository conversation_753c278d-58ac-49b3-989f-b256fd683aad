# 🤖 智能帧提取器

专为高铁巡检项目设计的智能帧提取工具，**无需手动调参**，程序会自动分析视频特征并选择最佳提取策略。

## ✨ 核心特性

- **⏱️ 按秒检查**: 每秒都检查，不跳过任何时间段
- **🎯 固定策略**: 每秒1-10帧，无需复杂调参
- **🚫 直接丢弃**: 模糊帧直接丢弃，不浪费存储
- **🧠 智能优选**: 每秒内自动选择最清晰的帧
- **📊 详细报告**: 生成完整的分析报告和可视化图表

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install opencv-python numpy matplotlib
```

### 2. 运行智能提取
```bash
python smart_frame_extractor.py
```

就这么简单！程序会自动：
- 分析您的视频质量
- 选择最佳提取参数
- 提取最清晰的帧
- 生成分析报告

## 🧠 智能算法

### 固定策略设计
- **每秒检查**: 不跳过任何秒，确保时间覆盖完整
- **每秒1-10帧**: 根据质量自动选择每秒保留的帧数
- **直接丢弃模糊帧**: 模糊帧不参与后续处理，确保质量
- **按秒优选**: 每秒内选择清晰度最高的帧

### 质量评估算法
- **清晰度计算**: 拉普拉斯方差(80%) + 梯度幅值(20%)
- **模糊检测**: 拉普拉斯方差阈值，直接过滤
- **质量排序**: 每秒内按清晰度排序选择最佳帧

### 智能参数选择

| 视频质量 | 模糊阈值 | 质量保留 | 每秒帧数 |
|---------|---------|---------|---------|
| **高质量** | 严格(70) | 前70% | 1-6帧 |
| **中等质量** | 中等(50) | 前80% | 1-8帧 |
| **低质量** | 宽松(30) | 前90% | 1-10帧 |

### 提取策略
1. **第一遍扫描**: 丢弃所有模糊帧
2. **质量分析**: 计算剩余帧的质量分布
3. **按秒分组**: 将帧按秒分组处理
4. **每秒优选**: 每秒选择1-10帧最清晰的帧

## 📊 输出结果

每次运行会在 `assets` 下创建时间戳目录：

```
assets/
└── frames_smart_20241231_143022/
    ├── frames/                      # 提取的清晰帧
    │   ├── smart_000001_f00000123_t004.12s_q045.67.jpg
    │   ├── smart_000002_f00000156_t005.20s_q048.23.jpg
    │   └── ...
    ├── smart_analysis_report.json   # 智能分析报告
    └── smart_analysis.png          # 可视化图表
```

### 文件命名
- `smart_序号_f原始帧号_t时间戳_q清晰度分数.jpg`
- 例如: `smart_000001_f00000123_t004.12s_q045.67.jpg`

## 📈 分析报告

智能分析报告包含：

```json
{
  "extraction_info": {
    "extraction_mode": "smart_auto",
    "total_frames_extracted": 1250,
    "extraction_rate_fps": 8.5
  },
  "video_analysis": {
    "quality_stats": { "mean": 297.5, "std": 85.2 },
    "motion_stats": { "blur_ratio": 0.15 }
  },
  "smart_parameters": {
    "quality_level": "good",
    "quality_threshold_percentile": 25,
    "max_frames_per_second": 8
  },
  "quality_summary": {
    "recommended_use": "适合精确分析和高质量目标识别"
  }
}
```

## 🎯 适用场景

### 高铁巡检项目
- ✅ 自动识别设备清晰图像
- ✅ 过滤晃动模糊帧
- ✅ 保持时间覆盖完整性
- ✅ 适配不同拍摄条件

### 其他应用
- 监控视频关键帧提取
- 行车记录仪事件检测
- 工业检测视频分析
- 医疗影像质量筛选

## 🔍 与传统方法对比

| 特性 | 传统方法 | 智能提取器 |
|------|---------|-----------|
| **参数调整** | 需要手动试错 | 自动优化 |
| **适应性** | 固定参数 | 动态调整 |
| **质量评估** | 单一指标 | 多维分析 |
| **使用难度** | 需要专业知识 | 一键运行 |
| **结果稳定性** | 依赖经验 | 算法保证 |

## 💡 使用建议

1. **首次使用**: 直接运行，查看分析报告了解视频特征
2. **批量处理**: 程序会为每个视频自动选择最佳策略
3. **质量检查**: 查看生成的可视化图表验证提取效果
4. **后续处理**: 提取的帧可直接用于目标识别模型

## 🛠️ 故障排除

### 常见问题

**Q: 提取帧数太少**
A: 程序检测到视频质量较差，这是正常的保护机制

**Q: 提取帧数太多**  
A: 程序检测到视频质量很好，会保留更多高质量帧

**Q: 程序运行时间长**
A: 智能分析需要扫描整个视频，这是为了获得最佳效果

### 性能优化
- 程序会根据视频长度自动调整采样策略
- 使用非交互式图表后端，避免界面卡住
- 内存优化的两遍扫描算法

---

**🤖 智能化，零调参，专业效果！**
