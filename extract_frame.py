import cv2
import numpy as np
import os
import math
from tqdm import tqdm

def variance_of_laplacian(gray: np.ndarray) -> float:
    # 输入灰度图 float32/uint8 都可
    lap = cv2.Laplacian(gray, cv2.CV_32F, ksize=3)
    return float(lap.var())

def mean_optical_flow_mag(prev_gray: np.ndarray, gray: np.ndarray, downscale=2) -> float:
    # 为提速与稳健性，先下采样到较小尺寸再算光流
    if downscale > 1:
        prev_gray_small = cv2.resize(prev_gray, (prev_gray.shape[1]//downscale, prev_gray.shape[0]//downscale))
        gray_small = cv2.resize(gray, (gray.shape[1]//downscale, gray.shape[0]//downscale))
    else:
        prev_gray_small, gray_small = prev_gray, gray

    flow = cv2.calcOpticalFlowFarneback(
        prev=prev_gray_small, next=gray_small,
        flow=None, pyr_scale=0.5, levels=3, winsize=15,
        iterations=3, poly_n=5, poly_sigma=1.2, flags=0
    )
    mag, ang = cv2.cartToPolar(flow[...,0], flow[...,1])
    return float(np.mean(mag))

def exposure_weight(gray: np.ndarray) -> float:
    # 简易曝光权重：过暗/过亮轻度惩罚 (0.6~1.0 之间)
    hist = cv2.calcHist([gray], [0], None, [256], [0,256]).ravel()
    hist /= max(hist.sum(), 1)
    dark = hist[:16].sum()      # 非常暗像素占比
    bright = hist[240:].sum()   # 非常亮像素占比
    penalty = 0.2*dark + 0.2*bright
    return float(max(0.6, 1.0 - penalty))

def frame_quality_score(gray: np.ndarray, prev_gray: np.ndarray|None, alpha=1.5) -> tuple[float, dict]:
    sharp = variance_of_laplacian(gray)
    flow = mean_optical_flow_mag(prev_gray, gray) if prev_gray is not None else 0.0
    expo = exposure_weight(gray)
    score = sharp * math.exp(-alpha * flow) * expo
    return score, {"sharpness": sharp, "flow": flow, "exposure_w": expo}

def select_clear_frames(
    video_path: str,
    out_dir: str = "clear_frames",
    top_k: int = 50,
    sample_every: int = 1,     # 每隔多少帧采样；视频很长可设为2/3等
    alpha: float = 1.5,        # 光流惩罚强度：越大越偏好不晃的帧
    min_interval_s: float = 0  # 同一秒只取一张时可用：>0会做“防连拍”
):
    os.makedirs(out_dir, exist_ok=True)
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        raise RuntimeError(f"Cannot open video: {video_path}")

    fps = cap.get(cv2.CAP_PROP_FPS) or 30.0
    prev_gray = None
    candidates = []  # (score, frame_idx, timestamp_sec, meta)

    frame_idx = 0
    pbar_total = int(cap.get(cv2.CAP_PROP_FRAME_COUNT) or 0)
    pbar = tqdm(total=pbar_total if pbar_total>0 else None, desc="Scanning frames")

    while True:
        ret, frame = cap.read()
        if not ret: break
        if frame_idx % sample_every != 0:
            frame_idx += 1
            pbar.update(1)
            continue

        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        gray = gray.astype(np.uint8)

        score, meta = frame_quality_score(gray, prev_gray, alpha=alpha)
        t = frame_idx / fps
        candidates.append((score, frame_idx, t, meta))

        prev_gray = gray
        frame_idx += 1
        pbar.update(1)

    cap.release()
    pbar.close()

    # 若需要“同一时间段只取一张”，可先做时间分桶再各取最优
    if min_interval_s > 0:
        buckets = {}
        for score, idx, t, meta in candidates:
            key = int(t // min_interval_s)
            if key not in buckets or score > buckets[key][0]:
                buckets[key] = (score, idx, t, meta)
        filtered = list(buckets.values())
    else:
        filtered = candidates

    # 取 top_k
    filtered.sort(key=lambda x: x[0], reverse=True)
    chosen = filtered[:top_k]

    # 逐帧回放保存
    cap = cv2.VideoCapture(video_path)
    saved = 0
    idx_set = {idx for _, idx, _, _ in chosen}
    pbar = tqdm(total=len(idx_set), desc="Saving frames")
    i = 0
    while True:
        ret, frame = cap.read()
        if not ret: break
        if i in idx_set:
            # 找元数据
            rec = next(r for r in chosen if r[1] == i)
            score, _, t, meta = rec
            fname = f"frame_{i:06d}_t{t:.2f}s_S{score:.0f}_sh{meta['sharpness']:.0f}_fl{meta['flow']:.3f}.jpg"
            cv2.imwrite(os.path.join(out_dir, fname), frame)
            saved += 1
            pbar.update(1)
            if saved >= len(idx_set): break
        i += 1
    cap.release()
    pbar.close()

    print(f"Done. Saved {saved} frames to: {out_dir}")

if __name__ == "__main__":
    import argparse
    ap = argparse.ArgumentParser()
    ap.add_argument("video", help="path to input video")
    ap.add_argument("--out", default="clear_frames", help="output folder")
    ap.add_argument("--topk", type=int, default=50, help="how many frames to keep")
    ap.add_argument("--sample-every", type=int, default=1, help="sample every N frames")
    ap.add_argument("--alpha", type=float, default=1.5, help="optical-flow penalty strength")
    ap.add_argument("--min-interval", type=float, default=0.0, help="min seconds between chosen frames")
    args = ap.parse_args()

    select_clear_frames(
        video_path=args.video,
        out_dir=args.out,
        top_k=args.topk,
        sample_every=args.sample_every,
        alpha=args.alpha,
        min_interval_s=args.min_interval
    )
