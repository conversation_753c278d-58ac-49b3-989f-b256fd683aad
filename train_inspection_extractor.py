#!/usr/bin/env python3
"""
高铁巡检视频清晰帧提取器
专门为高铁巡检项目优化，保留更多清晰帧用于目标识别
"""

import cv2
import numpy as np
import os
import json
import matplotlib.pyplot as plt
from pathlib import Path
from typing import List, Dict, Tuple
from datetime import datetime


class TrainInspectionFrameExtractor:
    """高铁巡检视频帧提取器"""
    
    def __init__(self, video_path: str = "assets/video/inspection.mp4", 
                 output_dir: str = "inspection_frames"):
        self.video_path = video_path
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 检查视频文件
        if not os.path.exists(video_path):
            raise FileNotFoundError(f"视频文件不存在: {video_path}")
        
        # 初始化视频捕获
        self.cap = cv2.VideoCapture(video_path)
        if not self.cap.isOpened():
            raise ValueError(f"无法打开视频文件: {video_path}")
        
        # 获取视频信息
        self.fps = self.cap.get(cv2.CAP_PROP_FPS)
        self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
        self.width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        self.height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        self.duration = self.total_frames / self.fps
        
        print(f"高铁巡检视频信息:")
        print(f"  文件: {video_path}")
        print(f"  分辨率: {self.width}x{self.height}")
        print(f"  帧率: {self.fps:.1f} FPS")
        print(f"  总帧数: {self.total_frames:,}")
        print(f"  时长: {self.duration:.1f} 秒")
        print(f"  输出目录: {self.output_dir}")
    
    def calculate_frame_sharpness(self, frame: np.ndarray, method: str = 'laplacian') -> float:
        """计算帧清晰度 - 针对巡检场景优化"""
        # 转换为灰度图
        if len(frame.shape) == 3:
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        else:
            gray = frame
        
        if method == 'laplacian':
            # 拉普拉斯方差 - 对边缘敏感，适合检测设备轮廓
            laplacian = cv2.Laplacian(gray, cv2.CV_64F)
            return laplacian.var()
        
        elif method == 'gradient':
            # 梯度幅值 - 适合检测整体清晰度
            grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
            grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
            magnitude = np.sqrt(grad_x**2 + grad_y**2)
            return magnitude.mean()
        
        elif method == 'combined':
            # 组合方法 - 为巡检优化
            # 拉普拉斯方差
            laplacian = cv2.Laplacian(gray, cv2.CV_64F)
            lap_var = laplacian.var()
            
            # 梯度幅值
            grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
            grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
            grad_mag = np.sqrt(grad_x**2 + grad_y**2).mean()
            
            # 加权组合 (拉普拉斯权重更高，因为边缘对巡检更重要)
            return lap_var * 0.7 + grad_mag * 0.3
        
        else:
            raise ValueError(f"不支持的方法: {method}")
    
    def detect_motion_blur(self, frame: np.ndarray, threshold: float = 100.0) -> bool:
        """检测运动模糊 - 过滤掉严重模糊的帧"""
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY) if len(frame.shape) == 3 else frame
        
        # 使用拉普拉斯算子检测模糊
        laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
        
        # 如果方差低于阈值，认为是模糊帧
        return laplacian_var < threshold
    
    def analyze_video_quality(self, sample_interval: int = 30) -> Dict:
        """分析整个视频的质量分布"""
        print(f"分析视频质量 (每{sample_interval}帧采样一次)...")
        
        frame_count = 0
        quality_scores = []
        blur_count = 0
        
        self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)  # 重置到开始
        
        while True:
            ret, frame = self.cap.read()
            if not ret:
                break
            
            if frame_count % sample_interval == 0:
                # 计算清晰度
                sharpness = self.calculate_frame_sharpness(frame, 'combined')
                quality_scores.append(sharpness)
                
                # 检测模糊
                if self.detect_motion_blur(frame):
                    blur_count += 1
            
            frame_count += 1
            
            if frame_count % 3000 == 0:  # 每100秒报告进度
                print(f"  已分析 {frame_count:,} 帧...")
        
        quality_scores = np.array(quality_scores)
        
        analysis = {
            'total_sampled_frames': len(quality_scores),
            'blur_frames': blur_count,
            'blur_percentage': (blur_count / len(quality_scores)) * 100,
            'quality_stats': {
                'mean': float(quality_scores.mean()),
                'std': float(quality_scores.std()),
                'min': float(quality_scores.min()),
                'max': float(quality_scores.max()),
                'median': float(np.median(quality_scores)),
                'q25': float(np.percentile(quality_scores, 25)),
                'q75': float(np.percentile(quality_scores, 75))
            }
        }
        
        print(f"视频质量分析完成:")
        print(f"  采样帧数: {analysis['total_sampled_frames']:,}")
        print(f"  模糊帧比例: {analysis['blur_percentage']:.1f}%")
        print(f"  清晰度分数 - 均值: {analysis['quality_stats']['mean']:.2f}, "
              f"标准差: {analysis['quality_stats']['std']:.2f}")
        
        return analysis
    
    def extract_clear_frames_for_inspection(self, 
                                          quality_threshold_percentile: float = 30,
                                          max_frames_per_second: int = 5,
                                          min_frame_interval: int = 3) -> List[Dict]:
        """
        为巡检优化的帧提取
        
        Args:
            quality_threshold_percentile: 质量阈值百分位数 (30表示保留质量前70%的帧)
            max_frames_per_second: 每秒最多保留的帧数
            min_frame_interval: 最小帧间隔 (避免连续相似帧)
        """
        print(f"开始提取巡检清晰帧...")
        print(f"  质量阈值: 前{100-quality_threshold_percentile}%")
        print(f"  每秒最多: {max_frames_per_second} 帧")
        print(f"  最小间隔: {min_frame_interval} 帧")
        
        # 首先分析视频质量分布
        analysis = self.analyze_video_quality(sample_interval=15)
        
        # 计算质量阈值
        quality_threshold = analysis['quality_stats']['mean'] - \
                          (analysis['quality_stats']['std'] * 
                           (quality_threshold_percentile / 100))
        
        print(f"  计算得出的质量阈值: {quality_threshold:.2f}")
        
        # 重置视频到开始
        self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
        
        selected_frames = []
        frame_count = 0
        last_selected_frame = -min_frame_interval
        frames_in_current_second = 0
        current_second = 0
        
        while True:
            ret, frame = self.cap.read()
            if not ret:
                break
            
            # 计算当前时间（秒）
            frame_second = int(frame_count / self.fps)
            
            # 如果进入新的一秒，重置计数
            if frame_second > current_second:
                current_second = frame_second
                frames_in_current_second = 0
            
            # 检查是否满足选择条件
            should_select = True
            
            # 1. 检查帧间隔
            if frame_count - last_selected_frame < min_frame_interval:
                should_select = False
            
            # 2. 检查每秒帧数限制
            if frames_in_current_second >= max_frames_per_second:
                should_select = False
            
            # 3. 检查是否严重模糊
            if self.detect_motion_blur(frame, threshold=50):
                should_select = False
            
            if should_select:
                # 计算清晰度
                sharpness = self.calculate_frame_sharpness(frame, 'combined')
                
                # 4. 检查质量阈值
                if sharpness >= quality_threshold:
                    timestamp = frame_count / self.fps
                    
                    selected_frames.append({
                        'frame_number': frame_count,
                        'timestamp': timestamp,
                        'sharpness': sharpness,
                        'frame': frame.copy()
                    })
                    
                    last_selected_frame = frame_count
                    frames_in_current_second += 1
            
            frame_count += 1
            
            # 进度报告
            if frame_count % 3000 == 0:
                progress = (frame_count / self.total_frames) * 100
                print(f"  进度: {progress:.1f}% - 已选择 {len(selected_frames)} 帧")
        
        print(f"帧提取完成!")
        print(f"  总帧数: {self.total_frames:,}")
        print(f"  选择帧数: {len(selected_frames):,}")
        print(f"  选择比例: {(len(selected_frames)/self.total_frames)*100:.2f}%")
        print(f"  平均每秒: {len(selected_frames)/self.duration:.1f} 帧")
        
        return selected_frames
    
    def save_inspection_frames(self, frames: List[Dict], prefix: str = "inspection") -> None:
        """保存巡检帧"""
        print(f"保存 {len(frames)} 帧到 {self.output_dir}...")
        
        # 按时间戳排序
        frames_sorted = sorted(frames, key=lambda x: x['timestamp'])
        
        # 创建子目录
        frames_dir = self.output_dir / "frames"
        frames_dir.mkdir(exist_ok=True)
        
        for i, frame_data in enumerate(frames_sorted):
            # 文件名包含更多信息
            filename = (f"{prefix}_{i+1:06d}_"
                       f"f{frame_data['frame_number']:08d}_"
                       f"t{frame_data['timestamp']:07.2f}s_"
                       f"q{frame_data['sharpness']:06.2f}.jpg")
            
            filepath = frames_dir / filename
            
            # 保存高质量图片
            cv2.imwrite(str(filepath), frame_data['frame'], 
                       [cv2.IMWRITE_JPEG_QUALITY, 95])
        
        print(f"帧已保存到: {frames_dir}")
        return frames_dir
    
    def create_inspection_report(self, frames: List[Dict], analysis: Dict = None) -> str:
        """创建巡检报告"""
        report = {
            'extraction_info': {
                'timestamp': datetime.now().isoformat(),
                'video_path': self.video_path,
                'total_frames_extracted': len(frames),
                'extraction_rate_fps': len(frames) / self.duration,
                'extraction_percentage': (len(frames) / self.total_frames) * 100
            },
            'video_info': {
                'resolution': f"{self.width}x{self.height}",
                'fps': self.fps,
                'total_frames': self.total_frames,
                'duration_seconds': self.duration
            },
            'quality_analysis': analysis,
            'extracted_frames': [
                {
                    'sequence': i + 1,
                    'frame_number': frame['frame_number'],
                    'timestamp': frame['timestamp'],
                    'sharpness_score': frame['sharpness']
                }
                for i, frame in enumerate(sorted(frames, key=lambda x: x['timestamp']))
            ]
        }
        
        # 保存报告
        report_path = self.output_dir / "inspection_report.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"巡检报告已保存: {report_path}")
        return str(report_path)
    
    def plot_extraction_analysis(self, frames: List[Dict]) -> None:
        """绘制提取分析图"""
        if not frames:
            print("没有帧数据可绘制")
            return
        
        timestamps = [f['timestamp'] for f in frames]
        sharpness_scores = [f['sharpness'] for f in frames]
        
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))
        
        # 时间分布图
        ax1.scatter(timestamps, sharpness_scores, alpha=0.6, s=20, c='blue')
        ax1.set_xlabel('时间 (秒)')
        ax1.set_ylabel('清晰度分数')
        ax1.set_title('高铁巡检 - 提取帧的时间分布和质量')
        ax1.grid(True, alpha=0.3)
        
        # 质量分布直方图
        ax2.hist(sharpness_scores, bins=50, alpha=0.7, color='green', edgecolor='black')
        ax2.set_xlabel('清晰度分数')
        ax2.set_ylabel('帧数')
        ax2.set_title('清晰度分数分布')
        ax2.grid(True, alpha=0.3)
        
        # 添加统计信息
        mean_quality = np.mean(sharpness_scores)
        ax2.axvline(mean_quality, color='red', linestyle='--', 
                   label=f'平均值: {mean_quality:.2f}')
        ax2.legend()
        
        plt.tight_layout()
        
        # 保存图表
        plot_path = self.output_dir / "extraction_analysis.png"
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')

        # 不显示图表，避免程序卡住
        # plt.show()  # 注释掉，避免在某些环境下卡住
        plt.close()  # 关闭图表释放内存

        print(f"分析图表已保存: {plot_path}")
    
    def __del__(self):
        """清理资源"""
        if hasattr(self, 'cap') and self.cap.isOpened():
            self.cap.release()


def main():
    """主函数 - 高铁巡检帧提取"""
    print("=" * 60)
    print("高铁巡检视频清晰帧提取器")
    print("=" * 60)
    
    try:
        # 创建提取器
        extractor = TrainInspectionFrameExtractor(
            video_path="assets/video/inspection.mp4",
            output_dir="inspection_clear_frames"
        )
        
        # 提取清晰帧 (保留更多帧用于巡检)
        clear_frames = extractor.extract_clear_frames_for_inspection(
            quality_threshold_percentile=20,  # 保留质量前80%的帧
            max_frames_per_second=8,          # 每秒最多8帧
            min_frame_interval=2              # 最小间隔2帧
        )
        
        # 保存帧
        frames_dir = extractor.save_inspection_frames(clear_frames)
        
        # 创建报告
        report_path = extractor.create_inspection_report(clear_frames)
        
        # 绘制分析图
        extractor.plot_extraction_analysis(clear_frames)
        
        print("\n" + "=" * 60)
        print("提取完成!")
        print(f"清晰帧保存在: {frames_dir}")
        print(f"分析报告: {report_path}")
        print("=" * 60)
        
    except Exception as e:
        print(f"错误: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
