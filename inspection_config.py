#!/usr/bin/env python3
"""
高铁巡检视频处理配置文件
根据您的具体需求调整这些参数
"""

# 视频文件配置
VIDEO_CONFIG = {
    'input_path': 'assets/video/inspection.mp4',
    'output_base_dir': 'assets',  # 基础输出目录
    'expected_fps': 30  # 您提到的30fps
}

# 帧提取配置 - 针对巡检目标识别优化
EXTRACTION_CONFIG = {
    # 质量阈值百分位数 (越小保留的帧越多)
    # 15 = 保留质量前85%的帧 (推荐用于目标识别)
    # 10 = 保留质量前90%的帧 (更多帧，可能包含一些较模糊的)
    # 25 = 保留质量前75%的帧 (更严格的质量要求)
    'quality_threshold_percentile': 15,
    
    # 每秒最多保留的帧数
    # 10 = 30fps -> 10fps (推荐，保持足够的时间分辨率)
    # 15 = 30fps -> 15fps (更多帧，用于精细分析)
    # 5 = 30fps -> 5fps (较少帧，用于快速浏览)
    'max_frames_per_second': 10,
    
    # 最小帧间隔 (避免连续相似帧)
    # 2 = 至少间隔2帧 (推荐)
    # 1 = 至少间隔1帧 (允许更密集的帧)
    # 3 = 至少间隔3帧 (更大间隔，减少相似帧)
    'min_frame_interval': 2,
    
    # 清晰度评估方法
    # 'combined' = 组合方法 (推荐用于巡检)
    # 'laplacian' = 拉普拉斯方差 (经典方法)
    # 'gradient' = 梯度幅值 (适合整体清晰度)
    'sharpness_method': 'combined',
    
    # 运动模糊检测阈值
    # 50 = 较严格 (过滤更多模糊帧)
    # 100 = 中等 (平衡)
    # 30 = 很严格 (只保留最清晰的帧)
    'motion_blur_threshold': 50
}

# 输出配置
OUTPUT_CONFIG = {
    # 图片质量 (JPEG质量，1-100)
    'jpeg_quality': 95,
    
    # 文件名前缀
    'filename_prefix': 'train_inspection',
    
    # 是否生成分析图表
    'generate_plots': True,
    
    # 是否生成详细报告
    'generate_report': True
}

# 不同场景的预设配置
PRESET_CONFIGS = {
    # 高质量模式 - 用于精确的目标识别
    'high_quality': {
        'quality_threshold_percentile': 25,
        'max_frames_per_second': 8,
        'min_frame_interval': 3,
        'motion_blur_threshold': 30
    },
    
    # 平衡模式 - 推荐用于一般巡检 (默认)
    'balanced': {
        'quality_threshold_percentile': 15,
        'max_frames_per_second': 10,
        'min_frame_interval': 2,
        'motion_blur_threshold': 50
    },
    
    # 高密度模式 - 保留更多帧用于全面分析
    'high_density': {
        'quality_threshold_percentile': 10,
        'max_frames_per_second': 15,
        'min_frame_interval': 1,
        'motion_blur_threshold': 70
    },
    
    # 快速浏览模式 - 少量高质量帧
    'quick_review': {
        'quality_threshold_percentile': 30,
        'max_frames_per_second': 5,
        'min_frame_interval': 5,
        'motion_blur_threshold': 30
    }
}

# 巡检特定配置
INSPECTION_SPECIFIC = {
    # 目标识别相关
    'target_detection': {
        'min_target_size_ratio': 0.01,  # 目标最小尺寸比例
        'roi_enabled': False,            # 是否启用感兴趣区域
        'roi_coordinates': None          # ROI坐标 (x, y, w, h)
    },
    
    # 视频分段处理 (如果视频很长)
    'segmentation': {
        'enabled': False,
        'segment_duration': 300,  # 每段5分钟
        'overlap_duration': 10    # 段间重叠10秒
    }
}

def get_config(preset='balanced'):
    """获取指定预设的配置"""
    config = EXTRACTION_CONFIG.copy()
    
    if preset in PRESET_CONFIGS:
        config.update(PRESET_CONFIGS[preset])
    
    return {
        'video': VIDEO_CONFIG,
        'extraction': config,
        'output': OUTPUT_CONFIG,
        'inspection': INSPECTION_SPECIFIC
    }

def print_config_info():
    """打印配置信息"""
    print("可用的预设配置:")
    print("=" * 50)
    
    for preset, config in PRESET_CONFIGS.items():
        print(f"\n{preset.upper()}:")
        for key, value in config.items():
            print(f"  {key}: {value}")
    
    print("\n" + "=" * 50)
    print("配置参数说明:")
    print("- quality_threshold_percentile: 越小保留帧越多")
    print("- max_frames_per_second: 每秒保留的最大帧数")
    print("- min_frame_interval: 帧之间的最小间隔")
    print("- motion_blur_threshold: 运动模糊检测阈值")

if __name__ == "__main__":
    print_config_info()
