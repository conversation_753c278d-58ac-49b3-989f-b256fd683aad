#!/usr/bin/env python3
"""
质量参数测试工具
快速测试不同参数对清晰度筛选的影响
"""

import cv2
import numpy as np
import os
from pathlib import Path
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt


def calculate_sharpness_enhanced(frame):
    """增强版清晰度计算"""
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY) if len(frame.shape) == 3 else frame
    
    # 拉普拉斯方差
    laplacian = cv2.Laplacian(gray, cv2.CV_64F)
    lap_var = laplacian.var()
    
    # 梯度幅值
    grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
    grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
    grad_mag = np.sqrt(grad_x**2 + grad_y**2).mean()
    
    # Brenner梯度
    brenner = np.abs(gray[2:, :] - gray[:-2, :]).mean()
    
    return lap_var * 0.6 + grad_mag * 0.25 + brenner * 0.15


def test_blur_threshold(video_path, motion_thresholds=[50, 80, 120, 150, 200]):
    """测试不同模糊阈值的效果"""
    
    print("🔍 测试模糊检测阈值效果...")
    
    cap = cv2.VideoCapture(video_path)
    fps = cap.get(cv2.CAP_PROP_FPS)
    
    # 采样前100帧进行快速测试
    test_frames = []
    frame_count = 0
    
    while frame_count < 100:
        ret, frame = cap.read()
        if not ret:
            break
        
        if frame_count % 5 == 0:  # 每5帧采样一次
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            blur_score = cv2.Laplacian(gray, cv2.CV_64F).var()
            sharpness = calculate_sharpness_enhanced(frame)
            
            test_frames.append({
                'frame_number': frame_count,
                'blur_score': blur_score,
                'sharpness': sharpness
            })
        
        frame_count += 1
    
    cap.release()
    
    print(f"采样了 {len(test_frames)} 帧进行测试")
    
    # 测试不同阈值
    results = {}
    for threshold in motion_thresholds:
        passed_frames = [f for f in test_frames if f['blur_score'] >= threshold]
        pass_rate = len(passed_frames) / len(test_frames) * 100
        
        if passed_frames:
            avg_sharpness = np.mean([f['sharpness'] for f in passed_frames])
            min_sharpness = np.min([f['sharpness'] for f in passed_frames])
            max_sharpness = np.max([f['sharpness'] for f in passed_frames])
        else:
            avg_sharpness = min_sharpness = max_sharpness = 0
        
        results[threshold] = {
            'pass_rate': pass_rate,
            'passed_count': len(passed_frames),
            'avg_sharpness': avg_sharpness,
            'min_sharpness': min_sharpness,
            'max_sharpness': max_sharpness
        }
        
        print(f"阈值 {threshold:3d}: 通过率 {pass_rate:5.1f}% ({len(passed_frames):2d}帧), "
              f"平均清晰度 {avg_sharpness:6.1f}")
    
    return results


def test_quality_threshold(video_path, quality_percentiles=[10, 20, 30, 40, 50]):
    """测试不同质量阈值的效果"""
    
    print("\n🎯 测试质量阈值效果...")
    
    cap = cv2.VideoCapture(video_path)
    
    # 采样帧计算清晰度分布
    sharpness_scores = []
    frame_count = 0
    
    while frame_count < 200:  # 采样更多帧
        ret, frame = cap.read()
        if not ret:
            break
        
        if frame_count % 3 == 0:
            # 先过基本模糊检测
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            blur_score = cv2.Laplacian(gray, cv2.CV_64F).var()
            
            if blur_score >= 80:  # 使用中等模糊阈值
                sharpness = calculate_sharpness_enhanced(frame)
                sharpness_scores.append(sharpness)
        
        frame_count += 1
    
    cap.release()
    
    if not sharpness_scores:
        print("❌ 没有帧通过模糊检测")
        return {}
    
    print(f"通过模糊检测的帧: {len(sharpness_scores)}")
    print(f"清晰度范围: {min(sharpness_scores):.1f} - {max(sharpness_scores):.1f}")
    print(f"清晰度均值: {np.mean(sharpness_scores):.1f}")
    
    # 测试不同质量阈值
    results = {}
    for percentile in quality_percentiles:
        threshold_value = np.percentile(sharpness_scores, percentile)
        passed_frames = [s for s in sharpness_scores if s >= threshold_value]
        pass_rate = len(passed_frames) / len(sharpness_scores) * 100
        
        results[percentile] = {
            'threshold_value': threshold_value,
            'pass_rate': pass_rate,
            'passed_count': len(passed_frames),
            'avg_sharpness': np.mean(passed_frames) if passed_frames else 0
        }
        
        print(f"百分位 {percentile:2d}%: 阈值 {threshold_value:6.1f}, "
              f"通过率 {pass_rate:5.1f}% ({len(passed_frames):2d}帧)")
    
    return results


def recommend_parameters(video_path):
    """推荐最佳参数"""
    
    print("\n🤖 分析并推荐最佳参数...")
    
    # 测试模糊阈值
    blur_results = test_blur_threshold(video_path)
    
    # 测试质量阈值  
    quality_results = test_quality_threshold(video_path)
    
    # 推荐逻辑
    print("\n💡 参数推荐:")
    
    # 推荐模糊阈值 (目标：保留30-60%的帧)
    best_blur_threshold = None
    for threshold, result in blur_results.items():
        if 30 <= result['pass_rate'] <= 60:
            best_blur_threshold = threshold
            break
    
    if not best_blur_threshold:
        # 如果没有合适的，选择通过率最接近45%的
        best_blur_threshold = min(blur_results.keys(), 
                                key=lambda x: abs(blur_results[x]['pass_rate'] - 45))
    
    # 推荐质量阈值 (目标：保留40-70%的帧)
    best_quality_percentile = None
    for percentile, result in quality_results.items():
        if 40 <= result['pass_rate'] <= 70:
            best_quality_percentile = percentile
            break
    
    if not best_quality_percentile:
        best_quality_percentile = min(quality_results.keys(),
                                    key=lambda x: abs(quality_results[x]['pass_rate'] - 55))
    
    print(f"推荐模糊阈值: {best_blur_threshold}")
    print(f"  - 通过率: {blur_results[best_blur_threshold]['pass_rate']:.1f}%")
    print(f"  - 平均清晰度: {blur_results[best_blur_threshold]['avg_sharpness']:.1f}")
    
    print(f"推荐质量百分位: {best_quality_percentile}%")
    print(f"  - 阈值: {quality_results[best_quality_percentile]['threshold_value']:.1f}")
    print(f"  - 通过率: {quality_results[best_quality_percentile]['pass_rate']:.1f}%")
    
    # 生成代码建议
    print(f"\n📝 代码修改建议:")
    print(f"将 motion_threshold 改为: {best_blur_threshold}")
    print(f"将 quality_threshold_percentile 改为: {best_quality_percentile}")
    
    return {
        'motion_threshold': best_blur_threshold,
        'quality_threshold_percentile': best_quality_percentile
    }


def create_quality_visualization(video_path, output_dir="quality_test_results"):
    """创建质量分析可视化"""
    
    Path(output_dir).mkdir(exist_ok=True)
    
    cap = cv2.VideoCapture(video_path)
    
    blur_scores = []
    sharpness_scores = []
    frame_numbers = []
    
    frame_count = 0
    while frame_count < 300:  # 采样300帧
        ret, frame = cap.read()
        if not ret:
            break
        
        if frame_count % 2 == 0:
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            blur_score = cv2.Laplacian(gray, cv2.CV_64F).var()
            sharpness = calculate_sharpness_enhanced(frame)
            
            blur_scores.append(blur_score)
            sharpness_scores.append(sharpness)
            frame_numbers.append(frame_count)
        
        frame_count += 1
    
    cap.release()
    
    # 创建可视化
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    
    # 模糊分数分布
    ax1.hist(blur_scores, bins=30, alpha=0.7, color='red', edgecolor='black')
    ax1.set_xlabel('模糊检测分数')
    ax1.set_ylabel('帧数')
    ax1.set_title('模糊检测分数分布')
    ax1.axvline(80, color='blue', linestyle='--', label='当前阈值: 80')
    ax1.axvline(120, color='green', linestyle='--', label='建议阈值: 120')
    ax1.legend()
    
    # 清晰度分数分布
    ax2.hist(sharpness_scores, bins=30, alpha=0.7, color='green', edgecolor='black')
    ax2.set_xlabel('清晰度分数')
    ax2.set_ylabel('帧数')
    ax2.set_title('清晰度分数分布')
    
    # 时间序列 - 模糊分数
    ax3.plot(frame_numbers, blur_scores, 'r-', alpha=0.7)
    ax3.set_xlabel('帧号')
    ax3.set_ylabel('模糊检测分数')
    ax3.set_title('模糊分数时间变化')
    ax3.axhline(80, color='blue', linestyle='--', alpha=0.5)
    
    # 时间序列 - 清晰度分数
    ax4.plot(frame_numbers, sharpness_scores, 'g-', alpha=0.7)
    ax4.set_xlabel('帧号')
    ax4.set_ylabel('清晰度分数')
    ax4.set_title('清晰度时间变化')
    
    plt.tight_layout()
    
    plot_path = Path(output_dir) / "quality_analysis.png"
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"📈 质量分析图表已保存: {plot_path}")


def main():
    video_path = "assets/video/inspection.mp4"
    
    if not os.path.exists(video_path):
        print(f"❌ 找不到视频文件: {video_path}")
        return
    
    print("🔧 质量参数测试工具")
    print("=" * 50)
    
    # 运行测试
    recommendations = recommend_parameters(video_path)
    
    # 创建可视化
    create_quality_visualization(video_path)
    
    print("\n" + "=" * 50)
    print("✅ 测试完成!")
    print("根据测试结果调整 smart_frame_extractor.py 中的参数")


if __name__ == "__main__":
    main()
